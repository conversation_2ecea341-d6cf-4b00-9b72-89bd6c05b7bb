<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="仪器设备安装调试记录" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center; font-size: 16px; font-weight: bold; padding: 15px;" colspan="4">仪器设备安装调试记录</th>
          </tr>
        </thead>
        <tbody>
          <!-- 第一行：名称、编号 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">名称</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.instrumentName || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">编号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.instrumentNo || '' }}</td>
          </tr>

          <!-- 第二行：型号、国别 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">型号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.instrumentModel || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">国别</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.country || '' }}</td>
          </tr>

          <!-- 第三行：制造厂、出厂编号 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">制造厂</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.manufacturer || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">出厂编号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.factoryNo || '' }}</td>
          </tr>

          <!-- 第四行：调试人、调试日期 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">调试人</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.creator_dictText || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">调试日期</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.createTime || '' }}</td>
          </tr>

          <!-- 第五行：调试记录 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">调试记录</td>
            <td colspan="3" style="text-align: left; padding: 8px; height: 300px; vertical-align: top;">
              <div style="white-space: pre-wrap; min-height: 280px;">{{ formData.instrumentAcceptTestDO.testRecord || '' }}</div>
            </td>
          </tr>

          <!-- 第六行：调试结论 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">调试结论</td>
            <td colspan="3" style="text-align: left; padding: 8px; height: 120px; vertical-align: top;">
              <div style="white-space: pre-wrap; min-height: 60px;">{{ formData.debugConclusion || '' }}</div>
              <div style="margin-top: 20px; display: flex; justify-content: space-between;">
                <span>调试人：{{ formData.instrumentAcceptTestDO.creator || '' }}</span>
                <span>日期：{{  formData.instrumentAcceptTestDO.createTime  || '' }}</span>
              </div>
            </td>
          </tr>

          <!-- 第七行：技术负责人 -->
          <tr>
            <td colspan="4" style="text-align: left; padding: 8px; height: 80px;">
              <div style="display: flex; justify-content: space-between; align-items: flex-end; height: 100%;">
                <span>技术负责人：{{ formData.techManagerSignature || '' }}</span>
                <span>日期：{{ formData.techManagerDate || '' }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);



// 定义表单数据
const formData = reactive<any>({
 
});

// 重置表单数据
const resetFormData = () => {
  // 基本信息
  formData.equipmentName = '';
  formData.equipmentNumber = '';
  formData.model = '';
  formData.country = '';
  formData.manufacturer = '';
  formData.serialNumber = '';

  // 调试信息
  formData.debugger = '';
  formData.debugDate = '';
  formData.debugRecord = '';
  formData.debugConclusion = '';
  formData.debuggerSignature = '';
  formData.debuggerDate = '';

  // 技术负责人签字信息
  formData.techManagerSignature = '';
  formData.techManagerDate = '';
};

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  // 先重置表单数据
  resetFormData();

  if (data && data.record) {
    Object.assign(formData,data.record)
    console.log("🚀 ~ formData:", formData)
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
