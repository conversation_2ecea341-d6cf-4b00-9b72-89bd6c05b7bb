<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    :title="modalTitle" 
    @ok="handleSubmit" 
    width="70%"
    :okText="okButtonText"
    :cancelText="'取消'"
  >
    <!-- 采购表单内容（只读） -->
    <div class="procurement-form-readonly">
      <h3 style="margin-bottom: 16px; color: #1890ff;">采购申请信息</h3>
      <a-form
        :model="procurementData"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-row class="form-row" :gutter="16">
          <!-- 申购类型 -->
          <a-col :span="20">
            <a-form-item label="申购类型">
              <a-input :value="procurementData.buyType" disabled />
            </a-form-item>
          </a-col>

          <!-- 申购资产名称 -->
          <a-col :span="20">
            <a-form-item label="申购资产名称">
              <a-input :value="procurementData.propertyName" disabled />
            </a-form-item>
          </a-col>

          <!-- 规格 -->
          <a-col :span="20">
            <a-form-item label="规格">
              <a-input :value="procurementData.spec" disabled />
            </a-form-item>
          </a-col>

          <!-- 数量 -->
          <a-col :span="20">
            <a-form-item label="数量">
              <a-input :value="procurementData.buyNumber" disabled />
            </a-form-item>
          </a-col>

          <!-- 单位 -->
          <a-col :span="20">
            <a-form-item label="单位">
              <a-input :value="procurementData.unit" disabled />
            </a-form-item>
          </a-col>

          <!-- 预估单价 -->
          <a-col :span="20">
            <a-form-item label="预估单价">
              <a-input :value="procurementData.singlePrice" disabled />
            </a-form-item>
          </a-col>

          <!-- 预估金额 -->
          <a-col :span="20">
            <a-form-item label="预估金额">
              <a-input :value="procurementData.estimatePrice" disabled />
            </a-form-item>
          </a-col>

          <!-- 部门类型 -->
          <a-col :span="20">
            <a-form-item label="部门类型">
              <a-input :value="procurementData.departmentType" disabled />
            </a-form-item>
          </a-col>

          <!-- 资产管理部门 -->
          <a-col :span="20">
            <a-form-item label="资产管理部门">
              <a-input :value="procurementData.propertyDepartment" disabled />
            </a-form-item>
          </a-col>

          <!-- 创建时间 -->
          <a-col :span="20">
            <a-form-item label="创建时间">
              <a-input :value="procurementData.createTime" disabled />
            </a-form-item>
          </a-col>

          <!-- 创建人 -->
          <a-col :span="20">
            <a-form-item label="创建人">
              <a-input :value="procurementData.creator" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 审批操作区域 -->
    <a-divider />
    <div class="approval-section">
      <h3 style="margin-bottom: 16px; color: #1890ff;">{{ approvalSectionTitle }}</h3>
      
      <!-- 审批操作选择 -->
      <a-form
        ref="approvalFormRef"
        :model="approvalForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :rules="approvalRules"
      >
        <a-form-item label="审批操作" name="action">
          <a-radio-group v-model:value="approvalForm.action" @change="onActionChange">
            <a-radio value="approve">通过</a-radio>
            <a-radio value="reject">驳回</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item 
          :label="approvalForm.action === 'approve' ? '审批意见' : '驳回原因'" 
          name="opinion"
        >
          <a-textarea
            v-model:value="approvalForm.opinion"
            :placeholder="approvalForm.action === 'approve' ? '请输入审批意见' : '请输入驳回原因'"
            :rows="4"
            show-count
            :maxlength="500"
          />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { defHttp } from '/@/utils/http/axios';

// Emits声明
const emit = defineEmits(['register', 'success']);

const approvalFormRef = ref<FormInstance>();
const approvalType = ref<'technical' | 'management'>('technical'); // 审批类型
const procurementData = ref<any>({}); // 采购数据

// 表单布局配置
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 6 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 18 },
});

// 审批表单数据
const approvalForm = reactive({
  action: 'approve', // approve: 通过, reject: 驳回
  opinion: '', // 审批意见或驳回原因
});

// 审批表单验证规则
const approvalRules = {
  action: [{ required: true, message: '请选择审批操作！' }],
  opinion: [{ required: true, message: '请输入审批意见或驳回原因！' }],
};

// 计算属性
const modalTitle = computed(() => {
  return approvalType.value === 'technical' ? '技术负责人审批' : '实验室管理人审批';
});

const approvalSectionTitle = computed(() => {
  return approvalType.value === 'technical' ? '技术负责人审批' : '实验室管理人审批';
});

const okButtonText = computed(() => {
  return approvalForm.action === 'approve' ? '通过' : '驳回';
});

// 弹框初始化
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  console.log('ApprovalModal data:', data);
  
  // 重置表单
  resetForm();
  
  // 设置审批类型和采购数据
  approvalType.value = data.approvalType || 'technical';
  procurementData.value = data.record || {};
  
  setModalProps({ confirmLoading: false });
});

// 重置表单
function resetForm() {
  approvalForm.action = 'approve';
  approvalForm.opinion = '';
}

// 审批操作变化处理
function onActionChange() {
  approvalForm.opinion = '';
}

// 提交审批
async function handleSubmit() {
  if (!approvalFormRef.value) return;

  try {
    await approvalFormRef.value.validate();
    setModalProps({ confirmLoading: true });

    // 构建请求参数
    const params: any = {
      id: procurementData.value.id,
    };

    if (approvalForm.action === 'approve') {
      // 通过审批
      if (approvalType.value === 'technical') {
        params.auditStatus = 1;
        params.auditContent = approvalForm.opinion;
      } else {
        params.auditStatus = 2;
        params.assignContent = approvalForm.opinion;
      }
    } else {
      // 驳回
      params.auditStatus = 99;
      params.rejectReason = approvalForm.opinion;
    }

    // 调用审批接口
    await defHttp.post({
      url: '/instrument/instrumentManagement/instrumentPurchaseAuditOrRollBack',
      params
    });

    message.success('审批操作成功！');
    closeModal();
    emit('success');
  } catch (error) {
    console.error('审批操作失败:', error);
    message.error('审批操作失败，请重试！');
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" scoped>
.procurement-form-readonly {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.approval-section {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.form-row {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input[disabled]) {
  background-color: #f5f5f5;
  color: #666;
}
</style>
